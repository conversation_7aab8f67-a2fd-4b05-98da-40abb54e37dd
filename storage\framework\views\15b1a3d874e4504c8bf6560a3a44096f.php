<?php $__env->startSection('title', 'Permission Management'); ?>

<?php
use Illuminate\Support\Facades\Storage;
?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Permission Management</h1>
            <p class="text-gray-600">Manage user permissions and role assignments</p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.permissions.roles')); ?>" 
               class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                </svg>
                Manage Roles
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Users</label>
                <input type="text" 
                       id="search" 
                       name="search" 
                       value="<?php echo e(request('search')); ?>"
                       placeholder="Search by name or email..."
                       class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            
            <div>
                <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Filter by Role</label>
                <select id="role" 
                        name="role" 
                        class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">All Roles</option>
                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($role->name); ?>" <?php echo e(request('role') === $role->name ? 'selected' : ''); ?>>
                            <?php echo e(ucfirst($role->name)); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" 
                        class="w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                    Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Permission Matrix Overview -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <div class="p-6 border-b border-neutral-200">
            <h2 class="text-lg font-semibold text-gray-900">Permission Matrix</h2>
            <p class="text-gray-600">Overview of permissions by role</p>
        </div>
        
        <div class="p-6 overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-neutral-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-900">Resource</th>
                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <th class="text-center py-3 px-4 font-medium text-gray-900">
                                <?php echo e(ucfirst($role->name)); ?>

                            </th>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $permissionMatrix; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $roleName => $resources): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($loop->first): ?>
                            <?php $__currentLoopData = $resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource => $actions): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="border-b border-neutral-100 hover:bg-neutral-50">
                                    <td class="py-3 px-4 font-medium text-gray-900">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $resource))); ?>

                                    </td>
                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <td class="py-3 px-4 text-center">
                                            <?php
                                                $rolePermissions = $permissionMatrix[$role->name][$resource] ?? [];
                                                $hasAnyPermission = collect($rolePermissions)->contains(true);
                                            ?>
                                            
                                            <?php if($hasAnyPermission): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                                    </svg>
                                                    Access
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                                    </svg>
                                                    No Access
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php break; ?>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Users List -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <div class="p-6 border-b border-neutral-200">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Users</h2>
                    <p class="text-gray-600">Manage individual user permissions</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="toggleBulkActions()" 
                            class="px-4 py-2 text-sm bg-neutral-100 text-neutral-700 rounded-lg hover:bg-neutral-200 transition-colors duration-200">
                        Bulk Actions
                    </button>
                </div>
            </div>
        </div>

        <!-- Bulk Actions (Hidden by default) -->
        <div id="bulk-actions" class="hidden p-6 bg-neutral-50 border-b border-neutral-200">
            <form id="bulk-form" class="flex items-center space-x-4">
                <div class="flex-1">
                    <select id="bulk-role" 
                            class="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Select Role</option>
                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($role->id); ?>"><?php echo e(ucfirst($role->name)); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <button type="button" 
                        onclick="bulkUpdateRoles()" 
                        class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200">
                    Update Selected
                </button>
            </form>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead class="bg-neutral-50">
                    <tr>
                        <th class="py-3 px-4 text-left">
                            <input type="checkbox" 
                                   id="select-all" 
                                   onchange="toggleAllUsers(this)"
                                   class="rounded border-neutral-300 text-primary-600 focus:ring-primary-500">
                        </th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-neutral-200">
                    <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-neutral-50">
                            <td class="py-4 px-4">
                                <input type="checkbox" 
                                       name="user_ids[]" 
                                       value="<?php echo e($user->id); ?>"
                                       class="user-checkbox rounded border-neutral-300 text-primary-600 focus:ring-primary-500">
                            </td>
                            <td class="py-4 px-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <?php if($user->avatar): ?>
                                            <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                                                 src="<?php echo e(Storage::url($user->avatar)); ?>"
                                                 alt="<?php echo e($user->full_name); ?>">
                                        <?php else: ?>
                                            <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center border-2 border-gray-200">
                                                <span class="text-sm font-medium text-primary-600">
                                                    <?php echo e(strtoupper(substr($user->first_name, 0, 1) . substr($user->last_name, 0, 1))); ?>

                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>

                                        </div>
                                        <div class="text-sm text-gray-500"><?php echo e($user->email); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="py-4 px-4">
                                <select onchange="updateUserRole(<?php echo e($user->id); ?>, this.value)" 
                                        class="text-sm border border-neutral-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($role->id); ?>" <?php echo e($user->role_id === $role->id ? 'selected' : ''); ?>>
                                            <?php echo e(ucfirst($role->name)); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </td>
                            <td class="py-4 px-4">
                                <?php if($user->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Inactive
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="py-4 px-4">
                                <a href="<?php echo e(route('admin.permissions.users.show', $user)); ?>" 
                                   class="text-primary-600 hover:text-primary-900 text-sm font-medium">
                                    View Details
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="py-8 px-4 text-center text-gray-500">
                                No users found.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if($users->hasPages()): ?>
            <div class="p-6 border-t border-neutral-200">
                <?php echo e($users->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function toggleBulkActions() {
    const bulkActions = document.getElementById('bulk-actions');
    bulkActions.classList.toggle('hidden');
}

function toggleAllUsers(checkbox) {
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    userCheckboxes.forEach(cb => cb.checked = checkbox.checked);
}

async function updateUserRole(userId, roleId) {
    try {
        const response = await fetch(`/admin/permissions/users/${userId}/role`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ role_id: roleId })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('User role updated successfully', 'success');
        } else {
            showNotification(data.message || 'Failed to update user role', 'error');
        }
    } catch (error) {
        showNotification('An error occurred while updating user role', 'error');
    }
}

async function bulkUpdateRoles() {
    const selectedUsers = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    const roleId = document.getElementById('bulk-role').value;

    if (selectedUsers.length === 0) {
        showNotification('Please select at least one user', 'warning');
        return;
    }

    if (!roleId) {
        showNotification('Please select a role', 'warning');
        return;
    }

    try {
        const response = await fetch('/admin/permissions/users/bulk-role-update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ 
                user_ids: selectedUsers,
                role_id: roleId 
            })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification(data.message, 'success');
            location.reload();
        } else {
            showNotification(data.message || 'Failed to update user roles', 'error');
        }
    } catch (error) {
        showNotification('An error occurred while updating user roles', 'error');
    }
}

function showNotification(message, type) {
    // Implementation depends on your notification system
    // This is a simple alert for now
    alert(message);
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/admin/permissions/index.blade.php ENDPATH**/ ?>