<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Http\Kernel');

echo "Testing blog post display...\n";

try {
    // Create a request to the blog post with the 'locale' parameter
    $locale = 'en';  // Adjust this as needed for your application
    $request = Illuminate\Http\Request::create("/$locale/blog/seo-best-practices-for-modern-websites", 'GET');
    
    // Process the request
    $response = $kernel->handle($request);
    
    echo "Status Code: " . $response->getStatusCode() . "\n";
    echo "Content Length: " . strlen($response->getContent()) . "\n";
    
    if ($response->getStatusCode() === 200) {
        echo "SUCCESS: Blog post loaded successfully!\n";
        
        // Check if the content contains the services section
        $content = $response->getContent();
        if (strpos($content, 'Related Services') !== false) {
            echo "SUCCESS: Services section found in content!\n";
        } else {
            echo "INFO: Services section not found (might be empty)\n";
        }
    } else {
        echo "ERROR: Unexpected status code: ". $response->getStatusCode() . "\n";
        echo "Response content: " . substr($response->getContent(), 0, 500) . "\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
