<?php $__env->startSection('title', 'Training Data Management'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Training Data Management Styles */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg;
    @apply transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.tab-button {
    @apply px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300;
}

.tab-button.active {
    @apply text-blue-600 border-blue-600;
}

.tab-content {
    @apply mt-6;
}

.tab-content.hidden {
    @apply hidden;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Training Data Management</h1>
            <p class="text-gray-600">Manage AI training data for improved chat responses</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="showImportModal()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Import Data
            </button>
            <button onclick="exportData()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export Data
            </button>
            <button onclick="showCreateModal()" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Training Data
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Entries</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo e(number_format($statistics['total'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Entries</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo e(number_format($statistics['active'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Inactive Entries</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo e(number_format($statistics['inactive'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Languages</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo e(count($statistics['by_language'])); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Filters & Search</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" id="search" placeholder="Search training data..." class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Intent</label>
                    <select id="filter-intent" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Intents</option>
                        <?php $__currentLoopData = $statistics['top_intents']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $intent => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($intent); ?>"><?php echo e(ucfirst(str_replace('_', ' ', $intent))); ?> (<?php echo e($count); ?>)</option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                    <select id="filter-language" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Languages</option>
                        <?php $__currentLoopData = $statistics['by_language']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($language); ?>"><?php echo e(strtoupper($language)); ?> (<?php echo e($count); ?>)</option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="filter-status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Status</option>
                        <option value="1">Active</option>
                        <option value="0">Inactive</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button onclick="applyFilters()" class="w-full btn-primary">Apply Filters</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Data Table -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Training Data Entries</h3>
                <div class="flex space-x-2">
                    <button onclick="bulkAction('activate')" class="btn-sm btn-secondary" disabled id="bulk-activate">Activate Selected</button>
                    <button onclick="bulkAction('deactivate')" class="btn-sm btn-secondary" disabled id="bulk-deactivate">Deactivate Selected</button>
                    <button onclick="bulkAction('delete')" class="btn-sm btn-danger" disabled id="bulk-delete">Delete Selected</button>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Intent</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Input Text</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expected Response</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Language</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="training-data-table" class="bg-white divide-y divide-gray-200">
                    <!-- Data will be loaded via JavaScript -->
                </tbody>
            </table>
        </div>
        <div id="pagination" class="px-6 py-3 border-t border-gray-200">
            <!-- Pagination will be loaded via JavaScript -->
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="training-data-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <h3 id="modal-title" class="text-lg font-medium text-gray-900">Add Training Data</h3>
            </div>
            <form id="training-data-form" class="p-6 space-y-4">
                <input type="hidden" id="training-data-id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Intent *</label>
                        <input type="text" id="intent" required class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Language *</label>
                        <select id="language" required class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Input Text *</label>
                    <textarea id="input_text" required rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Expected Response *</label>
                    <textarea id="expected_response" required rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <input type="text" id="category" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Confidence Threshold</label>
                        <input type="number" id="confidence_threshold" min="0" max="1" step="0.01" value="0.8" class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tags (comma-separated)</label>
                    <input type="text" id="tags" placeholder="tag1, tag2, tag3" class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>

                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="is_active" checked class="rounded border-gray-300">
                        <span class="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Save Training Data</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div id="import-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Import Training Data</h3>
            </div>
            <form id="import-form" class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Select File</label>
                    <input type="file" id="import-file" accept=".json,.csv" required class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <p class="text-sm text-gray-500 mt-1">Supported formats: JSON, CSV (max 10MB)</p>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeImportModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Import Data</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentFilters = {};
let selectedIds = [];

// Load training data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadTrainingData();
    
    // Setup event listeners
    document.getElementById('select-all').addEventListener('change', toggleSelectAll);
    document.getElementById('training-data-form').addEventListener('submit', saveTrainingData);
    document.getElementById('import-form').addEventListener('submit', importData);
});

// Load training data with pagination and filters
function loadTrainingData(page = 1) {
    const params = new URLSearchParams({
        page: page,
        per_page: 15,
        ...currentFilters
    });

    fetch(`<?php echo e(route('admin.chat.training-data.data')); ?>?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderTrainingDataTable(data.data.data);
                renderPagination(data.data);
                currentPage = page;
            }
        })
        .catch(error => {
            console.error('Error loading training data:', error);
            alert('Failed to load training data');
        });
}

// Render training data table
function renderTrainingDataTable(data) {
    const tbody = document.getElementById('training-data-table');
    tbody.innerHTML = '';

    data.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4">
                <input type="checkbox" class="row-checkbox rounded border-gray-300" value="${item.id}">
            </td>
            <td class="px-6 py-4 text-sm text-gray-900">${item.intent}</td>
            <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">${item.input_text}</td>
            <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">${item.expected_response}</td>
            <td class="px-6 py-4 text-sm text-gray-900">${item.language.toUpperCase()}</td>
            <td class="px-6 py-4">
                <span class="px-2 py-1 text-xs font-semibold rounded-full ${item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${item.is_active ? 'Active' : 'Inactive'}
                </span>
            </td>
            <td class="px-6 py-4 text-sm space-x-2">
                <button onclick="editTrainingData(${item.id})" class="text-blue-600 hover:text-blue-800">Edit</button>
                <button onclick="deleteTrainingData(${item.id})" class="text-red-600 hover:text-red-800">Delete</button>
            </td>
        `;
        tbody.appendChild(row);
    });

    // Setup row checkbox listeners
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedIds);
    });
}

// Apply filters
function applyFilters() {
    currentFilters = {
        search: document.getElementById('search').value,
        intent: document.getElementById('filter-intent').value,
        language: document.getElementById('filter-language').value,
        is_active: document.getElementById('filter-status').value,
    };

    // Remove empty filters
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });

    loadTrainingData(1);
}

// Show create modal
function showCreateModal() {
    document.getElementById('modal-title').textContent = 'Add Training Data';
    document.getElementById('training-data-form').reset();
    document.getElementById('training-data-id').value = '';
    document.getElementById('is_active').checked = true;
    document.getElementById('training-data-modal').classList.remove('hidden');
}

// Close modal
function closeModal() {
    document.getElementById('training-data-modal').classList.add('hidden');
}

// Save training data
function saveTrainingData(e) {
    e.preventDefault();
    
    const formData = {
        intent: document.getElementById('intent').value,
        input_text: document.getElementById('input_text').value,
        expected_response: document.getElementById('expected_response').value,
        language: document.getElementById('language').value,
        category: document.getElementById('category').value,
        confidence_threshold: parseFloat(document.getElementById('confidence_threshold').value),
        tags: document.getElementById('tags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
        is_active: document.getElementById('is_active').checked,
        _token: '<?php echo e(csrf_token()); ?>'
    };

    const id = document.getElementById('training-data-id').value;
    const url = id ? `<?php echo e(route('admin.chat.training-data.index')); ?>/${id}` : '<?php echo e(route('admin.chat.training-data.store')); ?>';
    const method = id ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            closeModal();
            loadTrainingData(currentPage);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error saving training data:', error);
        alert('Failed to save training data');
    });
}

// Edit training data
function editTrainingData(id) {
    fetch(`<?php echo e(route('admin.chat.training-data.index')); ?>/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const item = data.data;
                document.getElementById('modal-title').textContent = 'Edit Training Data';
                document.getElementById('training-data-id').value = item.id;
                document.getElementById('intent').value = item.intent;
                document.getElementById('input_text').value = item.input_text;
                document.getElementById('expected_response').value = item.expected_response;
                document.getElementById('language').value = item.language;
                document.getElementById('category').value = item.category || '';
                document.getElementById('confidence_threshold').value = item.confidence_threshold;
                document.getElementById('tags').value = (item.tags || []).join(', ');
                document.getElementById('is_active').checked = item.is_active;
                document.getElementById('training-data-modal').classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error loading training data:', error);
            alert('Failed to load training data');
        });
}

// Delete training data
function deleteTrainingData(id) {
    if (!confirm('Are you sure you want to delete this training data?')) {
        return;
    }

    fetch(`<?php echo e(route('admin.chat.training-data.index')); ?>/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            loadTrainingData(currentPage);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting training data:', error);
        alert('Failed to delete training data');
    });
}

// Update selected IDs
function updateSelectedIds() {
    selectedIds = Array.from(document.querySelectorAll('.row-checkbox:checked')).map(cb => parseInt(cb.value));
    
    const bulkButtons = ['bulk-activate', 'bulk-deactivate', 'bulk-delete'];
    bulkButtons.forEach(buttonId => {
        document.getElementById(buttonId).disabled = selectedIds.length === 0;
    });
}

// Toggle select all
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.row-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedIds();
}

// Bulk actions
function bulkAction(action) {
    if (selectedIds.length === 0) {
        alert('Please select at least one item');
        return;
    }

    if (!confirm(`Are you sure you want to ${action} ${selectedIds.length} selected items?`)) {
        return;
    }

    fetch('<?php echo e(route('admin.chat.training-data.bulk')); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify({
            action: action,
            ids: selectedIds,
            _token: '<?php echo e(csrf_token()); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            loadTrainingData(currentPage);
            selectedIds = [];
            document.getElementById('select-all').checked = false;
            updateSelectedIds();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error performing bulk action:', error);
        alert('Failed to perform bulk action');
    });
}

// Show import modal
function showImportModal() {
    document.getElementById('import-modal').classList.remove('hidden');
}

// Close import modal
function closeImportModal() {
    document.getElementById('import-modal').classList.add('hidden');
    document.getElementById('import-form').reset();
}

// Import data
function importData(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('file', document.getElementById('import-file').files[0]);
    formData.append('_token', '<?php echo e(csrf_token()); ?>');

    fetch('<?php echo e(route('admin.chat.training-data.import')); ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            closeImportModal();
            loadTrainingData(1);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error importing data:', error);
        alert('Failed to import data');
    });
}

// Export data
function exportData() {
    const params = new URLSearchParams(currentFilters);
    
    fetch(`<?php echo e(route('admin.chat.training-data.export')); ?>?${params}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // Trigger download
            window.open(data.download_url, '_blank');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error exporting data:', error);
        alert('Failed to export data');
    });
}

// Render pagination
function renderPagination(paginationData) {
    const pagination = document.getElementById('pagination');
    const { current_page, last_page, from, to, total } = paginationData;
    
    let paginationHtml = `
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing ${from || 0} to ${to || 0} of ${total} results
            </div>
            <div class="flex space-x-1">
    `;
    
    // Previous button
    if (current_page > 1) {
        paginationHtml += `<button onclick="loadTrainingData(${current_page - 1})" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">Previous</button>`;
    }
    
    // Page numbers
    for (let i = Math.max(1, current_page - 2); i <= Math.min(last_page, current_page + 2); i++) {
        const isActive = i === current_page;
        paginationHtml += `
            <button onclick="loadTrainingData(${i})" class="px-3 py-2 text-sm border border-gray-300 rounded-md ${isActive ? 'bg-blue-500 text-white' : 'hover:bg-gray-50'}">
                ${i}
            </button>
        `;
    }
    
    // Next button
    if (current_page < last_page) {
        paginationHtml += `<button onclick="loadTrainingData(${current_page + 1})" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">Next</button>`;
    }
    
    paginationHtml += `
            </div>
        </div>
    `;
    
    pagination.innerHTML = paginationHtml;
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/admin/chat/training-data/index.blade.php ENDPATH**/ ?>