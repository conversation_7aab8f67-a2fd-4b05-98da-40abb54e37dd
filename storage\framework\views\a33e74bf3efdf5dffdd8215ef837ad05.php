<?php $__env->startSection('title', 'AI Configuration'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">AI Configuration</h1>
            <p class="text-gray-600 mt-2">Configure and manage AI chatbot settings</p>
        </div>
        
        <!-- AI Status -->
        <div class="flex items-center space-x-4">
            <div class="flex items-center">
                <div class="w-3 h-3 <?php echo e($settings['enabled'] ? 'bg-green-500' : 'bg-red-500'); ?> rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">
                    AI <?php echo e($settings['enabled'] ? 'Enabled' : 'Disabled'); ?>

                </span>
            </div>
            <button onclick="testAIConnection()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                Test Connection
            </button>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg Response Time</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($performance['avg_response_time']); ?>s</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Success Rate</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($performance['success_rate']); ?>%</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Escalation Rate</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($performance['escalation_rate']); ?>%</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-soft border border-neutral-100 p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">User Satisfaction</p>
                    <p class="text-2xl font-bold text-gray-900"><?php echo e($performance['user_satisfaction']); ?>/5</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Tabs -->
    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
        <div class="border-b border-neutral-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button onclick="switchTab('settings')" id="tab-settings" class="tab-button active">
                    AI Settings
                </button>
                <button onclick="switchTab('templates')" id="tab-templates" class="tab-button">
                    Response Templates
                </button>
                <button onclick="switchTab('training')" id="tab-training" class="tab-button">
                    Training Data
                </button>
                <button onclick="switchTab('analytics')" id="tab-analytics" class="tab-button">
                    Analytics
                </button>
                <button onclick="switchTab('providers')" id="tab-providers" class="tab-button">
                    AI Providers
                </button>
            </nav>
        </div>

        <!-- AI Settings Tab -->
        <div id="content-settings" class="tab-content active">
            <div class="p-6">
                <form id="aiSettingsForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Settings -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900">Basic Settings</h3>
                            
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="ai_enabled" <?php echo e($settings['enabled'] ? 'checked' : ''); ?> 
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Enable AI Chatbot</span>
                                </label>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">AI Model</label>
                                <select id="model" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                    <option value="gpt-3.5-turbo" <?php echo e($settings['model'] === 'gpt-3.5-turbo' ? 'selected' : ''); ?>>GPT-3.5 Turbo</option>
                                    <option value="gpt-4" <?php echo e($settings['model'] === 'gpt-4' ? 'selected' : ''); ?>>GPT-4</option>
                                    <option value="gpt-4-turbo" <?php echo e($settings['model'] === 'gpt-4-turbo' ? 'selected' : ''); ?>>GPT-4 Turbo</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Temperature: <span id="temperature-value"><?php echo e($settings['temperature']); ?></span>
                                </label>
                                <input type="range" id="temperature" min="0" max="2" step="0.1" 
                                       value="<?php echo e($settings['temperature']); ?>" 
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>Conservative</span>
                                    <span>Creative</span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Tokens</label>
                                <input type="number" id="max_tokens" min="50" max="1000" 
                                       value="<?php echo e($settings['max_tokens']); ?>" 
                                       class="w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-900">Advanced Settings</h3>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Confidence Threshold: <span id="confidence-value"><?php echo e($settings['confidence_threshold']); ?></span>
                                </label>
                                <input type="range" id="confidence_threshold" min="0" max="1" step="0.1" 
                                       value="<?php echo e($settings['confidence_threshold']); ?>" 
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <p class="text-xs text-gray-500 mt-1">Minimum confidence to auto-respond</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Escalation Threshold: <span id="escalation-value"><?php echo e($settings['escalation_threshold']); ?></span>
                                </label>
                                <input type="range" id="escalation_threshold" min="0" max="1" step="0.1" 
                                       value="<?php echo e($settings['escalation_threshold']); ?>" 
                                       class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <p class="text-xs text-gray-500 mt-1">Confidence below which to escalate to human</p>
                            </div>

                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="greeting_enabled" <?php echo e($settings['greeting_enabled'] ? 'checked' : ''); ?> 
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Enable Automated Greetings</span>
                                </label>
                            </div>

                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="fallback_enabled" <?php echo e($settings['fallback_enabled'] ? 'checked' : ''); ?> 
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Enable Fallback to Human Agents</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="resetSettings()" class="btn-secondary">Reset</button>
                        <button type="submit" class="btn-primary">Save Settings</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Response Templates Tab -->
        <div id="content-templates" class="tab-content hidden">
            <div class="p-6">
                <form id="templatesForm">
                    <div class="space-y-6">
                        <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 capitalize">
                                <?php echo e(str_replace('_', ' ', $key)); ?> Template
                            </label>
                            <textarea id="template_<?php echo e($key); ?>" rows="3" 
                                      class="w-full border border-gray-300 rounded-md px-3 py-2"
                                      placeholder="Enter <?php echo e($key); ?> template..."><?php echo e($template); ?></textarea>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="resetTemplates()" class="btn-secondary">Reset</button>
                        <button type="submit" class="btn-primary">Save Templates</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Training Data Tab -->
        <div id="content-training" class="tab-content hidden">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900">Total Entries</h4>
                        <p class="text-2xl font-bold text-blue-600"><?php echo e($trainingData['total_entries']); ?></p>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900">Active Entries</h4>
                        <p class="text-2xl font-bold text-green-600"><?php echo e($trainingData['active_entries']); ?></p>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900">Languages</h4>
                        <p class="text-2xl font-bold text-purple-600"><?php echo e(count($trainingData['languages'])); ?></p>
                    </div>
                </div>

                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Training Data Management</h3>
                    <p class="text-gray-600 mb-4">Manage AI training data for improved chat responses</p>
                    <a href="<?php echo e(route('admin.chat.training-data.index')); ?>" class="btn-primary">Manage Training Data</a>
                </div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div id="content-analytics" class="tab-content hidden">
            <div class="p-6">
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">AI Analytics Dashboard</h3>
                    <p class="text-gray-600 mb-4">Detailed AI performance analytics and insights</p>
                    <a href="<?php echo e(route('admin.chat.ai.analytics-dashboard')); ?>" class="btn-primary">View Analytics Dashboard</a>
                </div>
            </div>
        </div>

        <!-- AI Providers Tab -->
        <div id="content-providers" class="tab-content hidden">
            <div class="p-6">
                <!-- Configuration Status Alert -->
                <div id="config-status-alert" class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg hidden">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Configuration Required</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Some AI providers are not properly configured. Please check your environment variables:</p>
                                <ul id="config-issues" class="mt-2 list-disc list-inside space-y-1"></ul>
                                <div class="mt-3">
                                    <button onclick="showConfigGuide()" class="text-yellow-800 underline hover:text-yellow-900">
                                        View Configuration Guide
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Provider Configuration -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Provider Configuration</h3>

                        <form id="providerSettingsForm">
                            <div class="space-y-4">
                                <!-- Default Provider -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Provider</label>
                                    <select id="default_provider" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                        <option value="openai" <?php echo e($currentProvider === 'openai' ? 'selected' : ''); ?>>OpenAI</option>
                                        <option value="anthropic" <?php echo e($currentProvider === 'anthropic' ? 'selected' : ''); ?>>Anthropic Claude</option>
                                        <option value="google" <?php echo e($currentProvider === 'google' ? 'selected' : ''); ?>>Google Gemini</option>
                                        <option value="xai" <?php echo e($currentProvider === 'xai' ? 'selected' : ''); ?>>xAI Grok</option>
                                    </select>
                                </div>

                                <!-- Provider Features -->
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="fallback_enabled" checked class="rounded border-gray-300 text-blue-600">
                                        <span class="ml-2 text-sm text-gray-700">Enable Provider Fallback</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="rate_limiting_enabled" checked class="rounded border-gray-300 text-blue-600">
                                        <span class="ml-2 text-sm text-gray-700">Enable Rate Limiting</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="caching_enabled" checked class="rounded border-gray-300 text-blue-600">
                                        <span class="ml-2 text-sm text-gray-700">Enable Response Caching</span>
                                    </label>
                                </div>

                                <!-- Model Selection -->
                                <div class="grid grid-cols-1 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">OpenAI Model</label>
                                        <select id="openai_model" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="gpt-4.1-mini">GPT-4.1 Mini</option>
                                            <option value="gpt-4.1">GPT-4.1</option>
                                            <option value="gpt-4o">GPT-4o</option>
                                            <option value="gpt-4o-mini">GPT-4o Mini</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Anthropic Model</label>
                                        <select id="anthropic_model" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="claude-sonnet-4-20250514">Claude Sonnet 4</option>
                                            <option value="claude-haiku-4-20250514">Claude Haiku 4</option>
                                            <option value="claude-opus-4-20250514">Claude Opus 4</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Google Model</label>
                                        <select id="google_model" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
                                            <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                                            <option value="gemini-2.5-pro-thinking">Gemini 2.5 Pro Thinking</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">xAI Model</label>
                                        <select id="xai_model" class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option value="grok-3">Grok 3</option>
                                            <option value="grok-2">Grok 2</option>
                                            <option value="grok-2-mini">Grok 2 Mini</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="flex justify-end space-x-3">
                                    <button type="button" onclick="testAllProviders()" class="btn-secondary">Test All</button>
                                    <button type="submit" class="btn-primary">Save Settings</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Provider Status & Usage -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Provider Status & Usage</h3>

                        <!-- Provider Cards -->
                        <div id="provider-status" class="space-y-4">
                            <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $providerKey => $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full" id="status-<?php echo e($providerKey); ?>"></div>
                                        <h4 class="font-medium text-gray-900"><?php echo e($provider['name']); ?></h4>
                                    </div>
                                    <button onclick="testProvider('<?php echo e($providerKey); ?>')" class="text-sm text-blue-600 hover:text-blue-800">Test</button>
                                </div>

                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">Models:</span>
                                        <span class="text-gray-900"><?php echo e(count($provider['models'])); ?></span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">Features:</span>
                                        <div class="flex space-x-1 mt-1">
                                            <?php if($provider['features']['vision']): ?>
                                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">Vision</span>
                                            <?php endif; ?>
                                            <?php if($provider['features']['function_calling']): ?>
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">Functions</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <?php if(isset($usageStats[$providerKey])): ?>
                                <div class="mt-3 pt-3 border-t border-gray-100">
                                    <div class="grid grid-cols-3 gap-2 text-xs">
                                        <div>
                                            <span class="text-gray-500">Requests Today:</span>
                                            <div class="font-medium"><?php echo e($usageStats[$providerKey]['requests_today'] ?? 0); ?></div>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Tokens Used:</span>
                                            <div class="font-medium"><?php echo e(number_format($usageStats[$providerKey]['tokens_used_today'] ?? 0)); ?></div>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Cost Today:</span>
                                            <div class="font-medium">$<?php echo e(number_format($usageStats[$providerKey]['cost_today'] ?? 0, 2)); ?></div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Quick Actions</h4>
                            <div class="space-y-2">
                                <button onclick="compareProviders()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                    Compare Provider Performance
                                </button>
                                <button onclick="refreshUsageStats()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                    Refresh Usage Statistics
                                </button>
                                <button onclick="exportProviderReport()" class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                    Export Provider Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Connection Modal -->
<div id="testModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Test AI Connection</h3>
            </div>
            <form id="testForm">
                <div class="px-6 py-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Test Message</label>
                    <textarea id="test_message" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2" 
                              placeholder="Enter a test message for the AI...">Hello, can you help me with information about ChiSolution?</textarea>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeTestModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Test AI</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Tab switching
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
        content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // Show selected tab content
    document.getElementById(`content-${tabName}`).classList.remove('hidden');
    document.getElementById(`content-${tabName}`).classList.add('active');
    
    // Add active class to selected tab button
    document.getElementById(`tab-${tabName}`).classList.add('active');
}

// Update range input displays
document.getElementById('temperature').addEventListener('input', function() {
    document.getElementById('temperature-value').textContent = this.value;
});

document.getElementById('confidence_threshold').addEventListener('input', function() {
    document.getElementById('confidence-value').textContent = this.value;
});

document.getElementById('escalation_threshold').addEventListener('input', function() {
    document.getElementById('escalation-value').textContent = this.value;
});

// AI Settings Form
document.getElementById('aiSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        enabled: document.getElementById('ai_enabled').checked,
        model: document.getElementById('model').value,
        temperature: parseFloat(document.getElementById('temperature').value),
        max_tokens: parseInt(document.getElementById('max_tokens').value),
        confidence_threshold: parseFloat(document.getElementById('confidence_threshold').value),
        escalation_threshold: parseFloat(document.getElementById('escalation_threshold').value),
        greeting_enabled: document.getElementById('greeting_enabled').checked,
        fallback_enabled: document.getElementById('fallback_enabled').checked,
        _token: '<?php echo e(csrf_token()); ?>'
    };
    
    fetch('<?php echo e(route("admin.chat.ai.settings.update")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Settings updated successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating settings');
    });
});

// Templates Form
document.getElementById('templatesForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const templates = {};
    <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    templates['<?php echo e($key); ?>'] = document.getElementById('template_<?php echo e($key); ?>').value;
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    
    const formData = {
        templates: templates,
        _token: '<?php echo e(csrf_token()); ?>'
    };
    
    fetch('<?php echo e(route("admin.chat.ai.templates.update")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Templates updated successfully!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating templates');
    });
});

// Test AI Connection
function testAIConnection() {
    document.getElementById('testModal').classList.remove('hidden');
}

function closeTestModal() {
    document.getElementById('testModal').classList.add('hidden');
}

document.getElementById('testForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const testMessage = document.getElementById('test_message').value;
    
    fetch('<?php echo e(route("admin.chat.ai.test")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify({
            test_message: testMessage,
            _token: '<?php echo e(csrf_token()); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        closeTestModal();
        
        if (data.success) {
            const result = data.data;
            alert(`AI Test Results:\n\nResponse: ${result.ai_response}\nConfidence: ${result.confidence}\nResponse Time: ${result.processing_time_ms}ms\nShould Escalate: ${result.should_escalate ? 'Yes' : 'No'}`);
        } else {
            alert('AI Test Failed: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred during AI testing');
        closeTestModal();
    });
});

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
        location.reload();
    }
}

function resetTemplates() {
    if (confirm('Are you sure you want to reset all templates to default values?')) {
        location.reload();
    }
}

// Provider Settings Form
document.getElementById('providerSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        default_provider: document.getElementById('default_provider').value,
        fallback_enabled: document.getElementById('fallback_enabled').checked,
        rate_limiting_enabled: document.getElementById('rate_limiting_enabled').checked,
        caching_enabled: document.getElementById('caching_enabled').checked,
        openai_model: document.getElementById('openai_model').value,
        anthropic_model: document.getElementById('anthropic_model').value,
        google_model: document.getElementById('google_model').value,
        xai_model: document.getElementById('xai_model').value,
        _token: '<?php echo e(csrf_token()); ?>'
    };

    fetch('<?php echo e(route("admin.chat.ai.provider-settings.update")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Provider settings updated successfully!');
            refreshUsageStats();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating provider settings');
    });
});

// Test individual provider
function testProvider(provider) {
    const testMessage = 'Hello, this is a test message from ChiSolution admin dashboard.';

    fetch('<?php echo e(route("admin.chat.ai.test-provider")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
        },
        body: JSON.stringify({
            provider: provider,
            message: testMessage,
            _token: '<?php echo e(csrf_token()); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const result = data.data;
            alert(`${provider.toUpperCase()} Test Results:\n\nResponse: ${result.response}\nModel: ${result.model}\nProcessing Time: ${result.processing_time_ms}ms\nTokens Used: ${result.tokens_used}\nConfidence: ${result.confidence}`);

            // Update status indicator
            document.getElementById(`status-${provider}`).className = 'w-3 h-3 bg-green-500 rounded-full';
        } else {
            let errorMessage = `${provider.toUpperCase()} Test Failed: ${data.message}`;

            if (data.type === 'configuration_error') {
                errorMessage += '\n\nThis is likely because the API key for this provider is not configured in your environment variables.';
                errorMessage += '\n\nPlease check your .env file and ensure the following variables are set:';

                switch(provider) {
                    case 'openai':
                        errorMessage += '\n- OPENAI_API_KEY\n- OPENAI_BASE_URL';
                        break;
                    case 'anthropic':
                        errorMessage += '\n- ANTHROPIC_API_KEY\n- ANTHROPIC_BASE_URL';
                        break;
                    case 'google':
                        errorMessage += '\n- GOOGLE_AI_API_KEY\n- GOOGLE_AI_BASE_URL';
                        break;
                    case 'xai':
                        errorMessage += '\n- XAI_API_KEY\n- XAI_BASE_URL';
                        break;
                }
            }

            alert(errorMessage);
            document.getElementById(`status-${provider}`).className = 'w-3 h-3 bg-red-500 rounded-full';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(`An error occurred while testing ${provider}`);
        document.getElementById(`status-${provider}`).className = 'w-3 h-3 bg-red-500 rounded-full';
    });
}

// Test all providers
function testAllProviders() {
    const providers = ['openai', 'anthropic', 'google', 'xai'];
    const testMessage = 'Hello, this is a comprehensive test of all AI providers.';

    if (!confirm('This will test all AI providers. Continue?')) {
        return;
    }

    providers.forEach(provider => {
        setTimeout(() => testProvider(provider), Math.random() * 2000);
    });
}

// Refresh usage statistics
function refreshUsageStats() {
    fetch('<?php echo e(route("admin.chat.ai.usage-stats")); ?>')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update usage stats in the UI
            console.log('Usage stats refreshed:', data.data);
            location.reload(); // Simple refresh for now
        }
    })
    .catch(error => {
        console.error('Error refreshing usage stats:', error);
    });
}

// Load analytics
function loadAnalytics() {
    fetch('<?php echo e(route("admin.chat.ai.analytics")); ?>?days=7')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Analytics data:', data.data);
            alert('Analytics loaded successfully! Check console for details.');
        } else {
            alert('Failed to load analytics: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error loading analytics:', error);
        alert('An error occurred while loading analytics');
    });
}

// Check provider configuration status
function checkProviderStatus() {
    fetch('<?php echo e(route("admin.chat.ai.provider-status")); ?>')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const status = data.data;
            const issues = [];

            Object.entries(status).forEach(([provider, info]) => {
                if (!info.configured) {
                    issues.push(`${provider.toUpperCase()}: ${info.message}`);
                }

                // Update status indicators
                const statusElement = document.getElementById(`status-${provider}`);
                if (statusElement) {
                    statusElement.className = info.configured ?
                        'w-3 h-3 bg-green-500 rounded-full' :
                        'w-3 h-3 bg-red-500 rounded-full';
                }
            });

            // Show configuration alert if there are issues
            const alertElement = document.getElementById('config-status-alert');
            const issuesElement = document.getElementById('config-issues');

            if (issues.length > 0) {
                issuesElement.innerHTML = issues.map(issue => `<li>${issue}</li>`).join('');
                alertElement.classList.remove('hidden');
            } else {
                alertElement.classList.add('hidden');
            }
        }
    })
    .catch(error => {
        console.error('Error checking provider status:', error);
    });
}

// Show configuration guide
function showConfigGuide() {
    const guide = `AI Provider Configuration Guide

To configure AI providers, add the following environment variables to your .env file:

OpenAI:
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

Anthropic Claude:
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com

Google Gemini:
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GOOGLE_AI_BASE_URL=https://generativelanguage.googleapis.com/v1beta

xAI Grok:
XAI_API_KEY=your_xai_api_key_here
XAI_BASE_URL=https://api.x.ai/v1

After adding these variables:
1. Restart your Laravel application
2. Clear the configuration cache: php artisan config:clear
3. Test the providers using the "Test" buttons

Note: You only need to configure the providers you plan to use.`;

    alert(guide);
}

// Initialize provider status check when providers tab is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check provider status when the page loads
    setTimeout(checkProviderStatus, 1000);
});

// Override switchTab function to check status when switching to providers tab
const originalSwitchTab = window.switchTab;
window.switchTab = function(tab) {
    originalSwitchTab(tab);
    if (tab === 'providers') {
        setTimeout(checkProviderStatus, 500);
    }
};
</script>

<style>
.tab-button {
    @apply py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
}

.tab-button.active {
    @apply border-blue-500 text-blue-600;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/admin/chat/ai/index.blade.php ENDPATH**/ ?>